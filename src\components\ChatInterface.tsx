import React, {
  useEffect,
  useState,
  useRef,
  useCallback,
  useMemo,
  useReducer,
} from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import useWebSocketChat from '@/hooks/useWebSocketChat';
import { useMarkAsRead } from '@/hooks/useMarkAsRead';
import { useToast } from '@/hooks/use-toast';
import { format, isValid } from 'date-fns';
import { WebSocketStatus } from '@/hooks/useWebSocket';
import { Message } from '@/store/api/chatApiSlice';

export interface NurseInfo {
  nurse_cognitoId: string;
  nurse_given_name: string;
}

export interface ChatInterfaceProps {
  // Modal mode props
  selectedNurse?: NurseInfo | null;
  isOpen?: boolean;
  onClose?: () => void;

  // Layout mode - 'modal' for overlay, 'fullscreen' for full page
  mode?: 'modal' | 'fullscreen';

  // Children render prop to pass chat state and handlers
  children: (chatState: ChatState) => React.ReactNode;
}

export interface ChatState {
  // Core state
  newMessage: string;
  setNewMessage: (message: string) => void;
  isTyping: boolean;
  conversationId: string | null;
  nurseInfo: NurseInfo | null;

  // WebSocket state
  wsStatus: WebSocketStatus;
  currentMessages: Message[];
  typingUsers: Record<
    string,
    { userId: string; userName: string; timestamp: number }
  >;

  // Loading states
  isLoading: boolean;
  isLoadingMessages: boolean;
  isCreatingConversation: boolean;
  isSendingMessage: boolean;

  // Error state
  error: string | null;

  // Handlers
  handleSendMessage: () => Promise<void>;
  handleKeyPress: (e: React.KeyboardEvent) => void;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleClose: () => void;
  handleBack: () => void;

  // Utilities
  safeFormatDate: (
    timestamp: string | undefined,
    formatString: string,
    fallback?: string
  ) => string;

  // Refs
  messagesEndRef: React.RefObject<HTMLDivElement>;

  // User info
  userId: string;
  userGivenName: string;
  displayNurseName: string;
}

export const ChatInterface: React.FC<ChatInterfaceProps> = ({
  selectedNurse,
  isOpen = true,
  onClose,
  mode = 'modal',
  children,
}) => {
  // Router hooks for fullscreen mode
  const { conversationId: routeConversationId } = useParams<{
    conversationId: string;
  }>();
  const location = useLocation();
  const navigate = useNavigate();

  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [typingTimeout, setTypingTimeout] = useState<NodeJS.Timeout | null>(
    null
  );
  const [conversationId, setConversationId] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const previousConversationIdRef = useRef<string | null>(null);
  const { toast } = useToast();

  // Determine nurse info based on mode
  const nurseInfo = useMemo(
    () =>
      mode === 'fullscreen'
        ? {
            nurse_cognitoId: location.state?.nurseId || '',
            nurse_given_name: location.state?.nurseName || 'Nurse',
          }
        : selectedNurse,
    [mode, location.state?.nurseId, location.state?.nurseName, selectedNurse]
  );

  // Use route conversation ID in fullscreen mode, state in modal mode
  const effectiveConversationId =
    mode === 'fullscreen' ? routeConversationId : conversationId;

  const safeFormatDate = (
    timestamp: string | undefined,
    formatString: string,
    fallback: string = '--'
  ) => {
    if (!timestamp) return fallback;

    const date = new Date(timestamp);
    if (!isValid(date)) return fallback;

    try {
      return format(date, formatString);
    } catch (error) {
      console.error('Error formatting date:', error);
      return fallback;
    }
  };

  const userId = localStorage.getItem('userId') || '';
  const userGivenName = localStorage.getItem('userGivenName') || 'Patient';
  const userType = 'patient';

  // WebSocket chat service
  const baseWsUrl =
    import.meta.env.VITE_CHAT_WS_URL || 'ws://localhost:8004';
  const wsUrl = baseWsUrl.endsWith('/ws')
    ? baseWsUrl
    : `${baseWsUrl.replace(/\/+$/, '')}/ws`;
  const token = localStorage.getItem('idToken') || '';

  // Keep WebSocket connection alive as long as user is logged in (has token and userId)
  const webSocketEnabled = !!(token && userId);

  const webSocketChat = useWebSocketChat({
    url: wsUrl,
    token,
    userId,
    userType,
    userName: userGivenName,
    enabled: webSocketEnabled,
  });

  const {
    status: wsStatus,
    conversations: _conversations,
    messages: wsMessages,
    loading,
    errors,
    getConversation: _getConversation,
    createConversation,
    getMessages,
    sendMessage,
    joinConversation,
    leaveConversation,
    sendTypingIndicator,
    sendReadReceipt,
    typingUsers,
    lastUpdate,
    messageCounter,
  } = webSocketChat;

  // Enhanced mark as read functionality with WebSocket support
  const markAsReadHook = useMarkAsRead({
    conversationId: effectiveConversationId,
    userId,
    debounceMs: 1000,
    sendReadReceipt: webSocketChat.sendReadReceipt,
    markMessagesAsRead: webSocketChat.markMessagesAsRead,
  });

  // Get messages for current conversation - use useMemo to ensure proper re-rendering
  const currentMessages = useMemo(() => {
    const messages = effectiveConversationId
      ? wsMessages[effectiveConversationId] || []
      : [];
    return messages;
  }, [effectiveConversationId, wsMessages]);

  // Force re-render when messages change for the current conversation
  const [, forceUpdate] = useReducer(x => x + 1, 0);

  useEffect(() => {
    if (effectiveConversationId && wsMessages[effectiveConversationId]) {
      forceUpdate();
    }
  }, [effectiveConversationId, wsMessages, lastUpdate, messageCounter]);

  const isLoadingMessages =
    loading[`messages-${effectiveConversationId}`] || false;
  const isCreatingConversation = loading['create-conversation'] || false;
  const isSendingMessage =
    loading[`send-message-${effectiveConversationId}`] || false;

  // Handle conversation creation for modal mode
  useEffect(() => {
    if (
      mode === 'modal' &&
      nurseInfo &&
      isOpen &&
      !conversationId &&
      wsStatus.connected
    ) {
      const createConversationForNurse = async () => {
        try {
          const response = await createConversation({
            nurseId: nurseInfo.nurse_cognitoId,
            nurseName: nurseInfo.nurse_given_name,
          });

          if (response.success && response.data?.conversation) {
            const conversation = response.data.conversation;
            const newConversationId =
              conversation.id || (conversation as { _id?: string })._id;
            setConversationId(newConversationId);
          } else {
            throw new Error(
              response.message || 'Failed to create conversation'
            );
          }
        } catch (error) {
          console.error('Error creating conversation:', error);
          toast({
            title: 'Chat Error',
            description:
              error instanceof Error
                ? error.message
                : 'Failed to create conversation',
            variant: 'destructive',
          });
          if (onClose) onClose();
        }
      };

      createConversationForNurse();
    }
  }, [
    mode,
    nurseInfo,
    isOpen,
    conversationId,
    wsStatus.connected,
    createConversation,
    onClose,
    toast,
  ]);

  // Set conversation ID from route in fullscreen mode
  useEffect(() => {
    if (mode === 'fullscreen' && routeConversationId) {
      setConversationId(routeConversationId);
    }
  }, [mode, routeConversationId]);

  // Handle joining conversation when component is active and WebSocket connects
  useEffect(() => {
    const shouldJoin = mode === 'fullscreen' ? true : isOpen;

    if (effectiveConversationId && wsStatus.connected && shouldJoin) {
      // Leave previous conversation if switching
      if (
        previousConversationIdRef.current &&
        previousConversationIdRef.current !== effectiveConversationId
      ) {
        leaveConversation(previousConversationIdRef.current);
      }

      // Join the conversation room for real-time messaging
      joinConversation(effectiveConversationId);
      previousConversationIdRef.current = effectiveConversationId;
    }
  }, [
    effectiveConversationId,
    wsStatus.connected,
    isOpen,
    mode,
    joinConversation,
    leaveConversation,
  ]);

  useEffect(() => {
    // Only leave conversation when switching to a different conversation or when component unmounts
    return () => {
      if (previousConversationIdRef.current && wsStatus.connected) {
        leaveConversation(previousConversationIdRef.current);
      }
    };
  }, [wsStatus.connected, leaveConversation]);

  // Load messages when conversation is available and user has joined the room
  useEffect(() => {
    const shouldLoad = mode === 'fullscreen' ? true : isOpen;

    if (effectiveConversationId && wsStatus.connected && shouldLoad) {
      // Add a small delay to ensure room joining has completed
      const loadMessages = async () => {
        try {
          await getMessages({
            conversationId: effectiveConversationId,
            page: 1,
            limit: 50,
          });
        } catch {
          toast({
            title: 'Connection Issue',
            description:
              'Failed to load messages. Please check your connection.',
            variant: 'destructive',
          });
        }
      };

      // Load messages after a brief delay to ensure room joining is complete
      const timeoutId = setTimeout(loadMessages, 100);
      return () => clearTimeout(timeoutId);
    } else {
      // Only show connection error if we're not connected AND not currently connecting
      // This prevents showing errors when the connection is being established
      if (
        effectiveConversationId &&
        !wsStatus.connected &&
        !wsStatus.connecting &&
        wsStatus.error &&
        wsStatus.reconnectAttempt > 0 // Only show after at least one reconnection attempt
      ) {
        toast({
          title: 'Connection Error',
          description: 'Chat connection lost. Trying to reconnect...',
          variant: 'destructive',
        });
      }
    }
  }, [
    effectiveConversationId,
    wsStatus.connected,
    wsStatus.connecting,
    wsStatus.error,
    wsStatus.reconnectAttempt,
    isOpen,
    mode,
    getMessages,
    toast,
  ]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [currentMessages]);

  // Track when chat interface becomes visible to mark messages as read
  const [hasBeenOpened, setHasBeenOpened] = useState(false);

  // Mark chat as opened when it becomes visible
  useEffect(() => {
    const shouldBeOpen = mode === 'fullscreen' ? true : isOpen;
    if (shouldBeOpen && effectiveConversationId && !hasBeenOpened) {
      setHasBeenOpened(true);
      // Don't automatically mark messages as read - let the intersection observer handle it
    }
  }, [isOpen, mode, effectiveConversationId, hasBeenOpened]);

  // Reset opened state when conversation changes
  useEffect(() => {
    setHasBeenOpened(false);
  }, [effectiveConversationId]);

  // Setup intersection observer for automatic read receipts only after chat has been opened
  useEffect(() => {
    const shouldSetup =
      (mode === 'fullscreen' ? true : isOpen) && hasBeenOpened;
    if (!effectiveConversationId || !shouldSetup) return;

    const messageElements = document.querySelectorAll('[data-message-id]');
    if (messageElements.length > 0) {
      markAsReadHook.setupMessageObserver(messageElements);
    }

    return () => {
      // Cleanup will be handled by the hook's useEffect
    };
  }, [
    effectiveConversationId,
    isOpen,
    mode,
    currentMessages,
    markAsReadHook,
    hasBeenOpened,
  ]);

  const handleTyping = useCallback(() => {
    if (!isTyping && effectiveConversationId) {
      setIsTyping(true);
      sendTypingIndicator(effectiveConversationId);
    }

    if (typingTimeout) {
      clearTimeout(typingTimeout);
    }

    const timeout = setTimeout(() => {
      setIsTyping(false);
    }, 3000);

    setTypingTimeout(timeout);
  }, [isTyping, effectiveConversationId, sendTypingIndicator, typingTimeout]);

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !effectiveConversationId || isSendingMessage)
      return;

    try {
      await sendMessage({
        conversationId: effectiveConversationId,
        content: newMessage.trim(),
        type: 'text',
      });

      setNewMessage('');
      setIsTyping(false);
      if (typingTimeout) {
        clearTimeout(typingTimeout);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: 'Error',
        description: 'Failed to send message',
        variant: 'destructive',
      });
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewMessage(e.target.value);
    handleTyping();
  };

  const handleClose = () => {
    setConversationId(null);
    setNewMessage('');
    setIsTyping(false);
    if (typingTimeout) {
      clearTimeout(typingTimeout);
    }
    if (onClose) onClose();
  };

  const handleBack = () => {
    navigate('/chat');
  };

  const isLoading =
    !wsStatus.connected ||
    isCreatingConversation ||
    (isLoadingMessages && currentMessages.length === 0);

  // Get display name for nurse
  const displayNurseName = nurseInfo?.nurse_given_name || 'Nurse';

  // Handle error state
  const error =
    errors[`conversation-${effectiveConversationId}`] ||
    errors[`messages-${effectiveConversationId}`];

  const chatState: ChatState = {
    newMessage,
    setNewMessage,
    isTyping,
    conversationId: effectiveConversationId,
    nurseInfo,
    wsStatus,
    currentMessages,
    typingUsers,
    isLoading,
    isLoadingMessages,
    isCreatingConversation,
    isSendingMessage,
    error,
    handleSendMessage,
    handleKeyPress,
    handleInputChange,
    handleClose,
    handleBack,
    safeFormatDate,
    messagesEndRef,
    userId,
    userGivenName,
    displayNurseName,
  };

  return <>{children(chatState)}</>;
};
